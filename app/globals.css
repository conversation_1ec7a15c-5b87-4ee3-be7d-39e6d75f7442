@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-primary-black text-white font-sans;
  }
}

:root {
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --accent-green: #00ff41;
  --accent-pink: #ff0080;
  --accent-blue: #00d4ff;
  --text-primary: #ffffff;
  --text-secondary: #a0a0a0;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

@layer components {
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-accent-green to-accent-pink bg-clip-text text-transparent;
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-accent-green/20;
  }
  
  .btn-primary {
    @apply bg-gradient-to-r from-accent-green to-accent-blue text-black px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:shadow-lg hover:shadow-accent-green/30 hover:scale-105;
  }
  
  .btn-secondary {
    @apply glass-effect text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:bg-white/20;
  }
  
  .glow-effect {
    @apply shadow-lg shadow-accent-green/50;
  }
}