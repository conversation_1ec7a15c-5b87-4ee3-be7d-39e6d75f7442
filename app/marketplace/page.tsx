'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Filter, Grid, List, Search, SlidersHorizontal } from 'lucide-react'
import Header from '@/components/Header'
import SkinCard from '@/components/SkinCard'
import FilterSidebar from '@/components/FilterSidebar'

interface Skin {
  id: string
  name: string
  weapon: string
  rarity: 'consumer' | 'industrial' | 'milspec' | 'restricted' | 'classified' | 'covert' | 'contraband'
  price: number
  priceChange: number
  image: string
  wear?: string
  statTrak?: boolean
  souvenir?: boolean
  float?: number
  pattern?: number
}

export default function MarketplacePage() {
  const [skins, setSkins] = useState<Skin[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState('price')
  const [sortOrder, setSortOrder] = useState('desc')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)

  useEffect(() => {
    fetchSkins()
  }, [searchQuery, sortBy, sortOrder, currentPage])

  const fetchSkins = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        search: searchQuery,
        sortBy,
        sortOrder,
        page: currentPage.toString(),
        limit: '12'
      })

      const response = await fetch(`/api/skins?${params}`)
      const data = await response.json()
      setSkins(data.skins)
    } catch (error) {
      console.error('Error fetching skins:', error)
    } finally {
      setLoading(false)
    }
  }

  const sortOptions = [
    { value: 'price', label: 'Price' },
    { value: 'name', label: 'Name' },
    { value: 'priceChange', label: 'Price Change' }
  ]

  return (
    <div className="min-h-screen bg-primary-black">
      <Header />
      
      {/* Page Header */}
      <div className="pt-24 pb-8 bg-gradient-to-b from-primary-black-light to-primary-black">
        <div className="max-w-container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <h1 className="text-headline mb-4">
              CS2 Skin <span className="gradient-text">Marketplace</span>
            </h1>
            <p className="text-xl text-gray-300">
              Browse and discover thousands of CS2 skins with real-time pricing
            </p>
          </motion.div>

          {/* Search and Controls */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="flex flex-col lg:flex-row gap-4 items-center justify-between"
          >
            {/* Search Bar */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search skins, weapons, collections..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 glass-effect rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-orange/50"
              />
            </div>

            {/* Controls */}
            <div className="flex items-center gap-4">
              {/* Sort */}
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newSortOrder] = e.target.value.split('-')
                  setSortBy(newSortBy)
                  setSortOrder(newSortOrder)
                }}
                className="glass-effect rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-accent-orange/50"
              >
                <option value="price-desc">Price: High to Low</option>
                <option value="price-asc">Price: Low to High</option>
                <option value="name-asc">Name: A to Z</option>
                <option value="name-desc">Name: Z to A</option>
                <option value="priceChange-desc">Trending Up</option>
                <option value="priceChange-asc">Trending Down</option>
              </select>

              {/* View Mode */}
              <div className="flex glass-effect rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${viewMode === 'grid' ? 'bg-accent-orange text-white' : 'text-gray-400 hover:text-white'}`}
                >
                  <Grid size={20} />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${viewMode === 'list' ? 'bg-accent-orange text-white' : 'text-gray-400 hover:text-white'}`}
                >
                  <List size={20} />
                </button>
              </div>

              {/* Filter Toggle */}
              <button
                onClick={() => setIsFilterOpen(true)}
                className="btn-secondary flex items-center gap-2"
              >
                <SlidersHorizontal size={20} />
                <span className="hidden sm:inline">Filters</span>
              </button>
            </div>
          </motion.div>
        </div>
      </div>

      <div className="max-w-container mx-auto px-4 py-8">
        <div className="flex gap-8">
          {/* Desktop Filter Sidebar */}
          <div className="hidden lg:block w-80">
            <div className="sticky top-24">
              <FilterSidebar isOpen={true} onClose={() => {}} />
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(12)].map((_, i) => (
                  <div key={i} className="glass-effect rounded-xl h-80 animate-pulse"></div>
                ))}
              </div>
            ) : (
              <motion.div
                className={`grid gap-6 ${
                  viewMode === 'grid' 
                    ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
                    : 'grid-cols-1'
                }`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                {skins.map((skin, index) => (
                  <motion.div
                    key={skin.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <SkinCard skin={skin} />
                  </motion.div>
                ))}
              </motion.div>
            )}

            {/* No Results */}
            {!loading && skins.length === 0 && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-16"
              >
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-2xl font-semibold mb-2">No skins found</h3>
                <p className="text-gray-400">Try adjusting your search or filters</p>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Filter Sidebar */}
      <FilterSidebar isOpen={isFilterOpen} onClose={() => setIsFilterOpen(false)} />
    </div>
  )
}