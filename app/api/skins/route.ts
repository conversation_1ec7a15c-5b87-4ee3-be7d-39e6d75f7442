import { NextRequest, NextResponse } from 'next/server'

// Mock data - in production this would come from MongoDB
const mockSkins = [
  {
    id: '1',
    name: 'Dragon Lore',
    weapon: 'AWP',
    rarity: 'covert',
    price: 4250.99,
    priceChange: 12.5,
    image: 'https://steamcommunity-a.akamaihd.net/economy/image/-9a81dlWLwJ2UUGcVs_nsVtzdOEdtWwKGZZLQHTxDZ7I56KU0Zwwo4NUX4oFJZEHLbXH5ApeO4YmlhxYQknCRvCo04DEVlxkKgpot621FAR17PLfYQJD_9W7m5a0mvLwOq7c2D4G65Ry3-qSrNj3jAXh_UVpYmGhJNKVcVQ2aV_T-gK9kOzxxcjrv5XJwXRj6D5iuygMsUGGn1gSOYYZiLXAHwuGWbJKVUy-n1s',
    wear: 'Field-Tested',
    float: 0.1847,
    pattern: 147,
    category: 'rifle',
    tags: ['dragon', 'lore', 'awp', 'sniper']
  },
  {
    id: '2',
    name: 'Howl',
    weapon: 'M4A4',
    rarity: 'contraband',
    price: 3890.50,
    priceChange: -2.3,
    image: 'https://steamcommunity-a.akamaihd.net/economy/image/-9a81dlWLwJ2UUGcVs_nsVtzdOEdtWwKGZZLQHTxDZ7I56KU0Zwwo4NUX4oFJZEHLbXH5ApeO4YmlhxYQknCRvCo04DEVlxkKgpou-6kejhz2v_Nfz5H_uO1gb-Gw_alDL_uh2xU7Mxkh6fA45W73QXj_kVrYmGgd4-Vc1M3aV3Z_1K6wOjxxcjrv5XJwXRj6D5iuygMsUGGn1gSOYYZiLXAHwuGWbJKVUy-n1s',
    wear: 'Minimal Wear',
    statTrak: true,
    float: 0.0892,
    category: 'rifle',
    tags: ['howl', 'm4a4', 'contraband', 'rare']
  },
  {
    id: '3',
    name: 'Fade',
    weapon: 'Karambit',
    rarity: 'covert',
    price: 2150.00,
    priceChange: 8.7,
    image: 'https://steamcommunity-a.akamaihd.net/economy/image/-9a81dlWLwJ2UUGcVs_nsVtzdOEdtWwKGZZLQHTxDZ7I56KU0Zwwo4NUX4oFJZEHLbXH5ApeO4YmlhxYQknCRvCo04DEVlxkKgpovbSsLQJf3qr3czxb49KzgL-YkvPLPr7Vn35cppMh3L2Qo9-g2wLi_0VuYjz7JoaRdlU7ZVyE_1K6wOjxxcjrv5XJwXRj6D5iuygMsUGGn1gSOYYZiLXAHwuGWbJKVUy-n1s',
    wear: 'Factory New',
    float: 0.0156,
    category: 'knife',
    tags: ['karambit', 'fade', 'knife', 'colorful']
  },
  {
    id: '4',
    name: 'Asiimov',
    weapon: 'AK-47',
    rarity: 'covert',
    price: 89.99,
    priceChange: 5.2,
    image: 'https://steamcommunity-a.akamaihd.net/economy/image/-9a81dlWLwJ2UUGcVs_nsVtzdOEdtWwKGZZLQHTxDZ7I56KU0Zwwo4NUX4oFJZEHLbXH5ApeO4YmlhxYQknCRvCo04DEVlxkKgpot7HxfDhjxszJemkV09-5lpKKqPrxN7LEmyUJ6ZAg2LiVrN6h2wDi_0VuYmGgd4-Vc1M3aV3Z_1K6wOjxxcjrv5XJwXRj6D5iuygMsUGGn1gSOYYZiLXAHwuGWbJKVUy-n1s',
    wear: 'Well-Worn',
    statTrak: true,
    float: 0.4234,
    category: 'rifle',
    tags: ['asiimov', 'ak47', 'futuristic', 'orange']
  },
  {
    id: '5',
    name: 'Redline',
    weapon: 'AK-47',
    rarity: 'classified',
    price: 45.50,
    priceChange: -1.8,
    image: 'https://steamcommunity-a.akamaihd.net/economy/image/-9a81dlWLwJ2UUGcVs_nsVtzdOEdtWwKGZZLQHTxDZ7I56KU0Zwwo4NUX4oFJZEHLbXH5ApeO4YmlhxYQknCRvCo04DEVlxkKgpot7HxfDhjxszJemkV08-jhIWZlP_1IbzTmGpD68N1mOzA-LP5gUBi7y1lNWGmJNKVcVQ2aV3Z_1K6wOjxxcjrv5XJwXRj6D5iuygMsUGGn1gSOYYZiLXAHwuGWbJKVUy-n1s',
    wear: 'Field-Tested',
    float: 0.2567,
    category: 'rifle',
    tags: ['redline', 'ak47', 'red', 'classic']
  },
  {
    id: '6',
    name: 'Hyper Beast',
    weapon: 'M4A1-S',
    rarity: 'covert',
    price: 125.75,
    priceChange: 15.3,
    image: 'https://steamcommunity-a.akamaihd.net/economy/image/-9a81dlWLwJ2UUGcVs_nsVtzdOEdtWwKGZZLQHTxDZ7I56KU0Zwwo4NUX4oFJZEHLbXH5ApeO4YmlhxYQknCRvCo04DEVlxkKgpou-6kejhz2v_Nfz5H_uO1gb-Gw_alDL_uh2xU7Mxkh6fA45W73QXj_kVrYmGgd4-Vc1M3aV3Z_1K6wOjxxcjrv5XJwXRj6D5iuygMsUGGn1gSOYYZiLXAHwuGWbJKVUy-n1s',
    wear: 'Minimal Wear',
    souvenir: true,
    float: 0.0789,
    category: 'rifle',
    tags: ['hyper', 'beast', 'm4a1s', 'colorful']
  }
]

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  
  // Extract query parameters
  const search = searchParams.get('search') || ''
  const category = searchParams.get('category') || ''
  const rarity = searchParams.get('rarity') || ''
  const minPrice = parseFloat(searchParams.get('minPrice') || '0')
  const maxPrice = parseFloat(searchParams.get('maxPrice') || '999999')
  const sortBy = searchParams.get('sortBy') || 'price'
  const sortOrder = searchParams.get('sortOrder') || 'desc'
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '12')

  // Filter skins
  let filteredSkins = mockSkins.filter(skin => {
    const matchesSearch = search === '' || 
      skin.name.toLowerCase().includes(search.toLowerCase()) ||
      skin.weapon.toLowerCase().includes(search.toLowerCase()) ||
      skin.tags.some(tag => tag.toLowerCase().includes(search.toLowerCase()))
    
    const matchesCategory = category === '' || skin.category === category
    const matchesRarity = rarity === '' || skin.rarity === rarity
    const matchesPrice = skin.price >= minPrice && skin.price <= maxPrice

    return matchesSearch && matchesCategory && matchesRarity && matchesPrice
  })

  // Sort skins
  filteredSkins.sort((a, b) => {
    let aValue, bValue
    
    switch (sortBy) {
      case 'price':
        aValue = a.price
        bValue = b.price
        break
      case 'name':
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case 'priceChange':
        aValue = a.priceChange
        bValue = b.priceChange
        break
      default:
        aValue = a.price
        bValue = b.price
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  // Paginate
  const startIndex = (page - 1) * limit
  const endIndex = startIndex + limit
  const paginatedSkins = filteredSkins.slice(startIndex, endIndex)

  return NextResponse.json({
    skins: paginatedSkins,
    pagination: {
      page,
      limit,
      total: filteredSkins.length,
      totalPages: Math.ceil(filteredSkins.length / limit)
    },
    filters: {
      categories: ['rifle', 'pistol', 'knife', 'glove'],
      rarities: ['consumer', 'industrial', 'milspec', 'restricted', 'classified', 'covert', 'contraband']
    }
  })
}