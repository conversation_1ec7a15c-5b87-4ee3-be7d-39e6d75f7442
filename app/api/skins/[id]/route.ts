import { NextRequest, NextResponse } from 'next/server'

// Mock detailed skin data
const mockSkinDetails = {
  '1': {
    id: '1',
    name: 'Dragon Lore',
    weapon: 'AWP',
    rarity: 'covert',
    price: 4250.99,
    priceChange: 12.5,
    image: 'https://steamcommunity-a.akamaihd.net/economy/image/-9a81dlWLwJ2UUGcVs_nsVtzdOEdtWwKGZZLQHTxDZ7I56KU0Zwwo4NUX4oFJZEHLbXH5ApeO4YmlhxYQknCRvCo04DEVlxkKgpot621FAR17PLfYQJD_9W7m5a0mvLwOq7c2D4G65Ry3-qSrNj3jAXh_UVpYmGhJNKVcVQ2aV_T-gK9kOzxxcjrv5XJwXRj6D5iuygMsUGGn1gSOYYZiLXAHwuGWbJKVUy-n1s',
    wear: 'Field-Tested',
    float: 0.1847,
    pattern: 147,
    category: 'rifle',
    tags: ['dragon', 'lore', 'awp', 'sniper'],
    description: 'It has been painted with a dragon design using a dry-transfer decal.',
    collection: 'The Cobblestone Collection',
    releaseDate: '2014-07-01',
    priceHistory: [
      { date: '2024-01-01', price: 3800 },
      { date: '2024-01-15', price: 3950 },
      { date: '2024-02-01', price: 4100 },
      { date: '2024-02-15', price: 4250.99 }
    ],
    marketplaces: [
      { name: 'Steam Market', price: 4250.99, available: true },
      { name: 'Skinport', price: 4180.50, available: true },
      { name: 'CS.MONEY', price: 4300.00, available: false }
    ],
    similarSkins: ['2', '3', '4']
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const skinId = params.id
  const skinDetails = mockSkinDetails[skinId as keyof typeof mockSkinDetails]

  if (!skinDetails) {
    return NextResponse.json(
      { error: 'Skin not found' },
      { status: 404 }
    )
  }

  return NextResponse.json(skinDetails)
}