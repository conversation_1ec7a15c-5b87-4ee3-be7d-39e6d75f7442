'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Trophy, Target, Zap, Crown, Medal, Award } from 'lucide-react'

interface LeaderboardPlayer {
  rank: number
  username: string
  kills: number
  deaths: number
  kd: number
  wins: number
  level: number
  xp: number
}

export default function Leaderboard() {
  const [activeTab, setActiveTab] = useState<'global' | 'weekly' | 'weapons'>('global')

  const globalLeaderboard: LeaderboardPlayer[] = [
    { rank: 1, username: 'ProGamer2024', kills: 2847, deaths: 1156, kd: 2.46, wins: 234, level: 45, xp: 15670 },
    { rank: 2, username: 'SniperElite', kills: 2634, deaths: 1289, kd: 2.04, wins: 198, level: 42, xp: 14230 },
    { rank: 3, username: 'FragMaster', kills: 2456, deaths: 1345, kd: 1.83, wins: 187, level: 39, xp: 13450 },
    { rank: 4, username: 'HeadshotKing', kills: 2234, deaths: 1123, kd: 1.99, wins: 176, level: 37, xp: 12890 },
    { rank: 5, username: '<PERSON><PERSON>', kills: 2156, deaths: 1234, kd: 1.75, wins: 165, level: 35, xp: 12340 },
    { rank: 6, username: 'CampMaster', kills: 2089, deaths: 1456, kd: 1.43, wins: 154, level: 33, xp: 11780 },
    { rank: 7, username: 'SprayNPray', kills: 1987, deaths: 1567, kd: 1.27, wins: 143, level: 31, xp: 11230 },
    { rank: 8, username: 'TacticalNinja', kills: 1876, deaths: 1234, kd: 1.52, wins: 132, level: 29, xp: 10670 },
    { rank: 9, username: 'QuickScope', kills: 1765, deaths: 1345, kd: 1.31, wins: 121, level: 27, xp: 10120 },
    { rank: 10, username: 'NoobSlayer', kills: 1654, deaths: 1456, kd: 1.14, wins: 110, level: 25, xp: 9560 }
  ]

  const weeklyLeaderboard = globalLeaderboard.map(player => ({
    ...player,
    kills: Math.floor(player.kills * 0.1),
    deaths: Math.floor(player.deaths * 0.1),
    wins: Math.floor(player.wins * 0.15)
  }))

  const weaponStats = [
    { weapon: 'AK-47', kills: 45670, usage: 34.5, avgDamage: 36, headshots: 11420 },
    { weapon: 'M4A4', kills: 37340, usage: 28.2, avgDamage: 33, headshots: 9335 },
    { weapon: 'AWP', kills: 20780, usage: 15.7, avgDamage: 115, headshots: 18702 },
    { weapon: 'Glock-18', kills: 16020, usage: 12.1, avgDamage: 28, headshots: 3204 },
    { weapon: 'Desert Eagle', kills: 12580, usage: 9.5, avgDamage: 63, headshots: 6290 }
  ]

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Crown className="text-yellow-400" size={24} />
      case 2: return <Medal className="text-gray-300" size={24} />
      case 3: return <Award className="text-amber-600" size={24} />
      default: return <span className="text-gray-400 font-bold">#{rank}</span>
    }
  }

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1: return 'border-yellow-400/50 bg-yellow-400/5'
      case 2: return 'border-gray-300/50 bg-gray-300/5'
      case 3: return 'border-amber-600/50 bg-amber-600/5'
      default: return 'border-white/10'
    }
  }

  return (
    <div className="min-h-screen bg-primary-black pt-20">
      <div className="max-w-6xl mx-auto px-4 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-6xl font-bold gradient-text mb-4">
            Leaderboard
          </h1>
          <p className="text-xl text-gray-300">
            Compete with the best players worldwide
          </p>
        </motion.div>

        {/* Tabs */}
        <div className="flex justify-center mb-8">
          <div className="glass-effect rounded-xl p-2 flex space-x-2">
            {[
              { id: 'global', label: 'Global', icon: Trophy },
              { id: 'weekly', label: 'Weekly', icon: Target },
              { id: 'weapons', label: 'Weapons', icon: Zap }
            ].map((tab) => (
              <motion.button
                key={tab.id}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-all duration-300 ${
                  activeTab === tab.id 
                    ? 'bg-gradient-to-r from-accent-green to-accent-blue text-black font-semibold' 
                    : 'text-gray-300 hover:text-white'
                }`}
                onClick={() => setActiveTab(tab.id as any)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <tab.icon size={20} />
                <span>{tab.label}</span>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Global Leaderboard */}
        {activeTab === 'global' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            {globalLeaderboard.map((player, index) => (
              <motion.div
                key={player.username}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`glass-effect rounded-xl p-6 border ${getRankColor(player.rank)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center justify-center w-12 h-12">
                      {getRankIcon(player.rank)}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white">{player.username}</h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-400">
                        <span>Level {player.level}</span>
                        <span>{player.xp.toLocaleString()} XP</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-4 gap-8 text-center">
                    <div>
                      <div className="text-accent-green font-bold text-lg">{player.kills.toLocaleString()}</div>
                      <div className="text-xs text-gray-400">Kills</div>
                    </div>
                    <div>
                      <div className="text-accent-pink font-bold text-lg">{player.deaths.toLocaleString()}</div>
                      <div className="text-xs text-gray-400">Deaths</div>
                    </div>
                    <div>
                      <div className="text-accent-blue font-bold text-lg">{player.kd.toFixed(2)}</div>
                      <div className="text-xs text-gray-400">K/D</div>
                    </div>
                    <div>
                      <div className="text-accent-green font-bold text-lg">{player.wins}</div>
                      <div className="text-xs text-gray-400">Wins</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Weekly Leaderboard */}
        {activeTab === 'weekly' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="text-center mb-6">
              <p className="text-gray-400">Week of December 18-24, 2024</p>
            </div>
            {weeklyLeaderboard.slice(0, 10).map((player, index) => (
              <motion.div
                key={player.username}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`glass-effect rounded-xl p-6 border ${getRankColor(index + 1)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center justify-center w-12 h-12">
                      {getRankIcon(index + 1)}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white">{player.username}</h3>
                      <div className="text-sm text-gray-400">This week's performance</div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-8 text-center">
                    <div>
                      <div className="text-accent-green font-bold text-lg">{player.kills}</div>
                      <div className="text-xs text-gray-400">Kills</div>
                    </div>
                    <div>
                      <div className="text-accent-pink font-bold text-lg">{player.deaths}</div>
                      <div className="text-xs text-gray-400">Deaths</div>
                    </div>
                    <div>
                      <div className="text-accent-green font-bold text-lg">{player.wins}</div>
                      <div className="text-xs text-gray-400">Wins</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Weapon Stats */}
        {activeTab === 'weapons' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            {weaponStats.map((weapon, index) => (
              <motion.div
                key={weapon.weapon}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="glass-effect rounded-xl p-6"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-accent-green to-accent-blue rounded-lg text-black font-bold">
                      #{index + 1}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white">{weapon.weapon}</h3>
                      <div className="text-sm text-gray-400">Most popular weapon</div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-4 gap-8 text-center">
                    <div>
                      <div className="text-accent-green font-bold text-lg">{weapon.kills.toLocaleString()}</div>
                      <div className="text-xs text-gray-400">Total Kills</div>
                    </div>
                    <div>
                      <div className="text-accent-blue font-bold text-lg">{weapon.usage}%</div>
                      <div className="text-xs text-gray-400">Usage Rate</div>
                    </div>
                    <div>
                      <div className="text-accent-pink font-bold text-lg">{weapon.avgDamage}</div>
                      <div className="text-xs text-gray-400">Avg Damage</div>
                    </div>
                    <div>
                      <div className="text-warning font-bold text-lg">{weapon.headshots.toLocaleString()}</div>
                      <div className="text-xs text-gray-400">Headshots</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </div>
    </div>
  )
}