'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Eye, EyeOff, User, Mail, Lock, GamepadIcon } from 'lucide-react'

interface LoginModalProps {
  isOpen: boolean
  onClose: () => void
  onLogin: (user: any) => void
}

export default function LoginModal({ isOpen, onClose, onLogin }: LoginModalProps) {
  const [isLogin, setIsLogin] = useState(true)
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState<any>({})

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setErrors({})

    try {
      const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register'
      const payload = isLogin 
        ? { email: formData.email, password: formData.password }
        : formData

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Mock successful response
      const mockUser = {
        id: '1',
        username: formData.username || 'Player' + Math.floor(Math.random() * 1000),
        email: formData.email,
        level: Math.floor(Math.random() * 50) + 1,
        xp: Math.floor(Math.random() * 10000),
        stats: {
          kills: Math.floor(Math.random() * 1000),
          deaths: Math.floor(Math.random() * 800),
          wins: Math.floor(Math.random() * 200),
          gamesPlayed: Math.floor(Math.random() * 500)
        }
      }

      onLogin(mockUser)
      onClose()
      
      // Show success message
      console.log(`✅ [${new Date().toLocaleTimeString('de-DE')}] User ${isLogin ? 'logged in' : 'registered'}: ${mockUser.username}`)
      
    } catch (error) {
      setErrors({ general: 'Authentication failed. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors((prev: any) => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/70 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            className="relative w-full max-w-md mx-4"
          >
            <div className="glass-effect rounded-2xl p-8 border border-white/20">
              {/* Header */}
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-accent-green to-accent-blue rounded-lg glow-effect">
                    <GamepadIcon size={24} className="text-black" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold gradient-text">
                      {isLogin ? 'Welcome Back' : 'Join DAVIO'}
                    </h2>
                    <p className="text-gray-400 text-sm">
                      {isLogin ? 'Sign in to continue playing' : 'Create your account'}
                    </p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                >
                  <X size={20} />
                </button>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Username (Register only) */}
                {!isLogin && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Username
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <input
                        type="text"
                        value={formData.username}
                        onChange={(e) => handleInputChange('username', e.target.value)}
                        className="w-full pl-10 pr-4 py-3 glass-effect rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-green/50 border border-white/10"
                        placeholder="Choose a username"
                        required={!isLogin}
                      />
                    </div>
                    {errors.username && (
                      <p className="text-accent-pink text-sm mt-1">{errors.username}</p>
                    )}
                  </div>
                )}

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Email
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full pl-10 pr-4 py-3 glass-effect rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-green/50 border border-white/10"
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                  {errors.email && (
                    <p className="text-accent-pink text-sm mt-1">{errors.email}</p>
                  )}
                </div>

                {/* Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Password
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      className="w-full pl-10 pr-12 py-3 glass-effect rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-green/50 border border-white/10"
                      placeholder="Enter your password"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                    >
                      {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="text-accent-pink text-sm mt-1">{errors.password}</p>
                  )}
                </div>

                {/* Confirm Password (Register only) */}
                {!isLogin && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Confirm Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <input
                        type={showPassword ? 'text' : 'password'}
                        value={formData.confirmPassword}
                        onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                        className="w-full pl-10 pr-4 py-3 glass-effect rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-green/50 border border-white/10"
                        placeholder="Confirm your password"
                        required={!isLogin}
                      />
                    </div>
                    {errors.confirmPassword && (
                      <p className="text-accent-pink text-sm mt-1">{errors.confirmPassword}</p>
                    )}
                  </div>
                )}

                {/* General Error */}
                {errors.general && (
                  <div className="p-3 bg-accent-pink/10 border border-accent-pink/20 rounded-lg">
                    <p className="text-accent-pink text-sm">{errors.general}</p>
                  </div>
                )}

                {/* Submit Button */}
                <motion.button
                  type="submit"
                  disabled={loading}
                  className="w-full btn-primary py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={{ scale: loading ? 1 : 1.02 }}
                  whileTap={{ scale: loading ? 1 : 0.98 }}
                >
                  {loading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin"></div>
                      <span>{isLogin ? 'Signing In...' : 'Creating Account...'}</span>
                    </div>
                  ) : (
                    isLogin ? 'Sign In' : 'Create Account'
                  )}
                </motion.button>

                {/* Toggle Mode */}
                <div className="text-center">
                  <button
                    type="button"
                    onClick={() => setIsLogin(!isLogin)}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {isLogin ? "Don't have an account? " : "Already have an account? "}
                    <span className="text-accent-green font-semibold">
                      {isLogin ? 'Sign Up' : 'Sign In'}
                    </span>
                  </button>
                </div>
              </form>

              {/* Quick Login (Development) */}
              <div className="mt-6 pt-6 border-t border-white/10">
                <p className="text-xs text-gray-500 text-center mb-3">Quick Login (Development)</p>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      setFormData({ ...formData, email: '<EMAIL>', password: 'password123' })
                    }}
                    className="flex-1 px-3 py-2 text-xs glass-effect rounded hover:bg-white/10 transition-colors"
                  >
                    Player
                  </button>
                  <button
                    onClick={() => {
                      setFormData({ ...formData, email: '<EMAIL>', password: 'password123' })
                    }}
                    className="flex-1 px-3 py-2 text-xs glass-effect rounded hover:bg-white/10 transition-colors"
                  >
                    Pro
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}