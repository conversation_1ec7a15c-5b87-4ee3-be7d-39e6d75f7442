'use client'

import { motion } from 'framer-motion'
import { Trophy, Target, Zap, Clock, Award, TrendingUp, Calendar, MapPin } from 'lucide-react'
import { useUserStore } from '@/store/userStore'

export default function UserProfile() {
  const { user, isAuthenticated } = useUserStore()

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen bg-primary-black pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold gradient-text mb-4">Access Denied</h1>
          <p className="text-gray-400">Please login to view your profile</p>
        </div>
      </div>
    )
  }

  const achievements = [
    { name: 'First Blood', description: 'Get your first kill', icon: Target, earned: true },
    { name: 'Killing Spree', description: 'Get 5 kills in a row', icon: Zap, earned: true },
    { name: 'Headshot Master', description: 'Get 100 headshots', icon: Trophy, earned: true },
    { name: 'Domination', description: 'Win 10 games in a row', icon: Award, earned: false },
    { name: 'Unstoppable', description: 'Get 25 kills in one game', icon: TrendingUp, earned: false },
    { name: 'Veteran', description: 'Play for 100 hours', icon: Clock, earned: false }
  ]

  const recentMatches = [
    { map: 'Arena', mode: 'Deathmatch', result: 'Win', kills: 18, deaths: 12, kd: 1.5, time: '2 hours ago' },
    { map: 'Desert', mode: 'Team DM', result: 'Loss', kills: 14, deaths: 16, kd: 0.88, time: '5 hours ago' },
    { map: 'Urban', mode: 'FFA', result: 'Win', kills: 22, deaths: 8, kd: 2.75, time: '1 day ago' },
    { map: 'Factory', mode: 'Deathmatch', result: 'Win', kills: 16, deaths: 11, kd: 1.45, time: '2 days ago' }
  ]

  return (
    <div className="min-h-screen bg-primary-black pt-20">
      <div className="max-w-6xl mx-auto px-4 py-8">
        
        {/* Profile Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="glass-effect rounded-2xl p-8 mb-8"
        >
          <div className="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
            {/* Avatar */}
            <div className="relative">
              <div className="w-32 h-32 bg-gradient-to-r from-accent-green to-accent-blue rounded-full flex items-center justify-center text-black font-bold text-4xl glow-effect">
                {user.username.charAt(0).toUpperCase()}
              </div>
              <div className="absolute -bottom-2 -right-2 bg-accent-green text-black px-3 py-1 rounded-full text-sm font-bold">
                LVL {user.level}
              </div>
            </div>

            {/* User Info */}
            <div className="flex-1 text-center md:text-left">
              <h1 className="text-4xl font-bold gradient-text mb-2">{user.username}</h1>
              <p className="text-gray-400 mb-4">{user.email}</p>
              
              <div className="flex flex-wrap justify-center md:justify-start gap-4 mb-6">
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Calendar size={16} />
                  <span>Joined December 2024</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <MapPin size={16} />
                  <span>Online</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Clock size={16} />
                  <span>Last seen: Now</span>
                </div>
              </div>

              {/* XP Progress */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-400">Experience</span>
                  <span className="text-sm text-accent-green">{user.xp} / {(user.level + 1) * 1000} XP</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-accent-green to-accent-blue h-2 rounded-full transition-all duration-500"
                    style={{ width: `${(user.xp % 1000) / 10}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-col space-y-3">
              <motion.button
                className="btn-primary px-6 py-3"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Edit Profile
              </motion.button>
              <motion.button
                className="btn-secondary px-6 py-3"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Share Profile
              </motion.button>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Stats */}
          <div className="lg:col-span-2 space-y-8">
            
            {/* Main Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="glass-effect rounded-xl p-6"
            >
              <h2 className="text-2xl font-bold gradient-text mb-6">Statistics</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-accent-green mb-2">{user.stats.kills}</div>
                  <div className="text-gray-400">Total Kills</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-accent-pink mb-2">{user.stats.deaths}</div>
                  <div className="text-gray-400">Deaths</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-accent-blue mb-2">{user.stats.kd}</div>
                  <div className="text-gray-400">K/D Ratio</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-accent-green mb-2">{user.stats.wins}</div>
                  <div className="text-gray-400">Wins</div>
                </div>
              </div>
            </motion.div>

            {/* Recent Matches */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="glass-effect rounded-xl p-6"
            >
              <h2 className="text-2xl font-bold gradient-text mb-6">Recent Matches</h2>
              <div className="space-y-4">
                {recentMatches.map((match, index) => (
                  <div key={index} className="flex items-center justify-between p-4 glass-effect rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className={`w-3 h-3 rounded-full ${match.result === 'Win' ? 'bg-accent-green' : 'bg-accent-pink'}`}></div>
                      <div>
                        <div className="font-semibold text-white">{match.map}</div>
                        <div className="text-sm text-gray-400">{match.mode}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-white">{match.kills}/{match.deaths}</div>
                      <div className="text-sm text-gray-400">K/D: {match.kd}</div>
                    </div>
                    <div className="text-sm text-gray-400">{match.time}</div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            
            {/* Achievements */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="glass-effect rounded-xl p-6"
            >
              <h2 className="text-xl font-bold gradient-text mb-6">Achievements</h2>
              <div className="space-y-4">
                {achievements.map((achievement, index) => (
                  <div key={index} className={`flex items-center space-x-3 p-3 rounded-lg ${
                    achievement.earned ? 'bg-accent-green/10 border border-accent-green/20' : 'bg-gray-800/50'
                  }`}>
                    <div className={`p-2 rounded-lg ${
                      achievement.earned ? 'bg-accent-green text-black' : 'bg-gray-700 text-gray-400'
                    }`}>
                      <achievement.icon size={16} />
                    </div>
                    <div className="flex-1">
                      <div className={`font-semibold ${achievement.earned ? 'text-white' : 'text-gray-400'}`}>
                        {achievement.name}
                      </div>
                      <div className="text-xs text-gray-500">{achievement.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Rank Progress */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="glass-effect rounded-xl p-6"
            >
              <h2 className="text-xl font-bold gradient-text mb-6">Rank Progress</h2>
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-accent-green to-accent-blue rounded-full flex items-center justify-center text-black font-bold text-xl mx-auto mb-4 glow-effect">
                  {user.level}
                </div>
                <div className="text-lg font-semibold text-white mb-2">Level {user.level}</div>
                <div className="text-sm text-gray-400 mb-4">
                  {1000 - (user.xp % 1000)} XP to next level
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-accent-green to-accent-blue h-2 rounded-full transition-all duration-500"
                    style={{ width: `${(user.xp % 1000) / 10}%` }}
                  ></div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}