'use client'

import { useState } from 'react'
import { Search, Menu, X, User, Gamepad2, Trophy, Zap, LogOut, Settings } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useUserStore } from '@/store/userStore'
import LoginModal from './LoginModal'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  
  const { user, isAuthenticated, login, logout } = useUserStore()

  const navItems = [
    { name: 'Play', href: '/play', icon: Gamepad2 },
    { name: 'Leaderboard', href: '/leaderboard', icon: Trophy },
    { name: 'Weapons', href: '/weapons', icon: Zap },
    { name: 'Profile', href: '/profile', icon: User },
  ]

  return (
    <header className="fixed top-0 w-full z-50 glass-effect border-b border-white/10">
      <div className="max-w-container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2"
          >
            <div className="w-8 h-8 bg-gradient-to-r from-accent-green to-accent-blue rounded-lg glow-effect"></div>
            <h1 className="text-2xl font-bold gradient-text">DAVIO</h1>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <motion.a
                key={item.name}
                href={item.href}
                className="text-gray-300 hover:text-white transition-colors duration-300 flex items-center space-x-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <item.icon size={18} />
                <span>{item.name}</span>
              </motion.a>
            ))}
          </nav>

          {/* Search Bar */}
          <div className="hidden md:flex items-center flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search players, rooms..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 glass-effect rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-green/50"
              />
            </div>
          </div>

          {/* User Account */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated && user ? (
              <div className="relative">
                <motion.button
                  className="flex items-center space-x-3 glass-effect px-4 py-2 rounded-lg"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowUserMenu(!showUserMenu)}
                >
                  <div className="w-8 h-8 bg-gradient-to-r from-accent-green to-accent-blue rounded-full flex items-center justify-center text-black font-bold">
                    {user.username.charAt(0).toUpperCase()}
                  </div>
                  <div className="text-left">
                    <div className="text-sm font-semibold text-white">{user.username}</div>
                    <div className="text-xs text-gray-400">Level {user.level}</div>
                  </div>
                </motion.button>

                {/* User Dropdown */}
                <AnimatePresence>
                  {showUserMenu && (
                    <motion.div
                      initial={{ opacity: 0, y: 10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: 10, scale: 0.95 }}
                      className="absolute right-0 top-full mt-2 w-64 glass-effect rounded-xl p-4 border border-white/20 z-50"
                    >
                      {/* User Info */}
                      <div className="mb-4 pb-4 border-b border-white/10">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-gradient-to-r from-accent-green to-accent-blue rounded-full flex items-center justify-center text-black font-bold text-lg">
                            {user.username.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <div className="font-semibold text-white">{user.username}</div>
                            <div className="text-sm text-gray-400">{user.email}</div>
                            <div className="text-xs text-accent-green">Level {user.level} • {user.xp} XP</div>
                          </div>
                        </div>
                      </div>

                      {/* Quick Stats */}
                      <div className="mb-4 grid grid-cols-2 gap-3">
                        <div className="text-center">
                          <div className="text-lg font-bold text-accent-green">{user.stats.kills}</div>
                          <div className="text-xs text-gray-400">Kills</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-accent-blue">{user.stats.kd}</div>
                          <div className="text-xs text-gray-400">K/D</div>
                        </div>
                      </div>

                      {/* Menu Items */}
                      <div className="space-y-2">
                        <motion.button
                          className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-white/10 transition-colors text-left"
                          whileHover={{ x: 5 }}
                        >
                          <User size={16} />
                          <span>Profile</span>
                        </motion.button>
                        <motion.button
                          className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-white/10 transition-colors text-left"
                          whileHover={{ x: 5 }}
                        >
                          <Settings size={16} />
                          <span>Settings</span>
                        </motion.button>
                        <motion.button
                          className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-white/10 transition-colors text-left text-accent-pink"
                          whileHover={{ x: 5 }}
                          onClick={() => {
                            logout()
                            setShowUserMenu(false)
                          }}
                        >
                          <LogOut size={16} />
                          <span>Logout</span>
                        </motion.button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ) : (
              <motion.button
                className="btn-primary px-6 py-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowLoginModal(true)}
              >
                Login
              </motion.button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <motion.button
            className="md:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            whileTap={{ scale: 0.95 }}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </motion.button>
        </div>

        {/* Mobile Search */}
        <div className="md:hidden mt-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search for skins..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 glass-effect rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-orange/50"
            />
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden glass-effect border-t border-white/10"
          >
            <div className="px-4 py-4 space-y-4">
              {navItems.map((item) => (
                <motion.a
                  key={item.name}
                  href={item.href}
                  className="flex items-center space-x-3 text-gray-300 hover:text-white transition-colors duration-300"
                  whileHover={{ x: 10 }}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <item.icon size={20} />
                  <span>{item.name}</span>
                </motion.a>
              ))}
              {isAuthenticated && user ? (
                <div className="pt-4 border-t border-white/10">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-accent-green to-accent-blue rounded-full flex items-center justify-center text-black font-bold">
                      {user.username.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div className="font-semibold text-white">{user.username}</div>
                      <div className="text-sm text-gray-400">Level {user.level}</div>
                    </div>
                  </div>
                  <motion.button
                    className="flex items-center space-x-3 text-accent-pink hover:text-white transition-colors duration-300 w-full"
                    whileHover={{ x: 10 }}
                    onClick={() => {
                      logout()
                      setIsMenuOpen(false)
                    }}
                  >
                    <LogOut size={20} />
                    <span>Logout</span>
                  </motion.button>
                </div>
              ) : (
                <motion.button
                  className="flex items-center space-x-3 text-gray-300 hover:text-white transition-colors duration-300 w-full"
                  whileHover={{ x: 10 }}
                  onClick={() => {
                    setShowLoginModal(true)
                    setIsMenuOpen(false)
                  }}
                >
                  <User size={20} />
                  <span>Login</span>
                </motion.button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLogin={login}
      />

      {/* Click outside to close user menu */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </header>
  )
}