'use client'

import { motion } from 'framer-motion'
import { Heart, TrendingUp, TrendingDown, Star } from 'lucide-react'
import Image from 'next/image'

interface SkinCardProps {
  skin: {
    id: string
    name: string
    weapon: string
    rarity: 'consumer' | 'industrial' | 'milspec' | 'restricted' | 'classified' | 'covert' | 'contraband'
    price: number
    priceChange: number
    image: string
    wear?: string
    statTrak?: boolean
    souvenir?: boolean
    float?: number
    pattern?: number
  }
}

const rarityColors = {
  consumer: 'text-rarity-consumer border-rarity-consumer',
  industrial: 'text-rarity-industrial border-rarity-industrial',
  milspec: 'text-rarity-milspec border-rarity-milspec',
  restricted: 'text-rarity-restricted border-rarity-restricted',
  classified: 'text-rarity-classified border-rarity-classified',
  covert: 'text-rarity-covert border-rarity-covert',
  contraband: 'text-rarity-contraband border-rarity-contraband',
}

export default function SkinCard({ skin }: SkinCardProps) {
  const isPositiveChange = skin.priceChange >= 0

  return (
    <motion.div
      className="glass-effect rounded-xl overflow-hidden card-hover group cursor-pointer"
      whileHover={{ y: -5 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Image Container */}
      <div className="relative aspect-[4/3] overflow-hidden">
        <Image
          src={skin.image}
          alt={`${skin.weapon} | ${skin.name}`}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-110"
        />
        
        {/* Rarity Border */}
        <div className={`absolute inset-0 border-t-4 ${rarityColors[skin.rarity]}`}></div>
        
        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {skin.statTrak && (
            <span className="bg-accent-orange text-white text-xs px-2 py-1 rounded font-semibold">
              StatTrak™
            </span>
          )}
          {skin.souvenir && (
            <span className="bg-warning text-black text-xs px-2 py-1 rounded font-semibold">
              Souvenir
            </span>
          )}
        </div>

        {/* Favorite Button */}
        <motion.button
          className="absolute top-2 right-2 p-2 glass-effect rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Heart size={16} className="text-white hover:text-accent-red transition-colors" />
        </motion.button>

        {/* Price Change Indicator */}
        <div className="absolute bottom-2 right-2">
          <div className={`flex items-center space-x-1 px-2 py-1 rounded text-xs font-semibold ${
            isPositiveChange ? 'bg-success/20 text-success' : 'bg-accent-red/20 text-accent-red'
          }`}>
            {isPositiveChange ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
            <span>{isPositiveChange ? '+' : ''}{skin.priceChange.toFixed(1)}%</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Weapon & Name */}
        <div className="mb-3">
          <h3 className="font-semibold text-white text-sm mb-1">
            {skin.weapon}
          </h3>
          <p className="text-gray-300 text-xs leading-tight">
            {skin.name}
          </p>
        </div>

        {/* Wear & Float */}
        {(skin.wear || skin.float) && (
          <div className="flex items-center justify-between mb-3 text-xs text-gray-400">
            {skin.wear && <span>{skin.wear}</span>}
            {skin.float && <span>Float: {skin.float.toFixed(4)}</span>}
          </div>
        )}

        {/* Pattern */}
        {skin.pattern && (
          <div className="mb-3 text-xs text-gray-400">
            Pattern: #{skin.pattern}
          </div>
        )}

        {/* Price */}
        <div className="flex items-center justify-between">
          <div className="text-right">
            <div className="text-lg font-bold text-white">
              €{skin.price.toLocaleString('de-DE', { minimumFractionDigits: 2 })}
            </div>
          </div>
          
          {/* Rarity Indicator */}
          <div className={`w-3 h-3 rounded-full ${rarityColors[skin.rarity].replace('text-', 'bg-').replace('border-', '')}`}></div>
        </div>
      </div>
    </motion.div>
  )
}