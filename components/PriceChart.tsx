'use client'

import { motion } from 'framer-motion'
import { TrendingUp, TrendingDown } from 'lucide-react'

interface PricePoint {
  date: string
  price: number
}

interface PriceChartProps {
  data: PricePoint[]
  currentPrice: number
  priceChange: number
}

export default function PriceChart({ data, currentPrice, priceChange }: PriceChartProps) {
  const isPositive = priceChange >= 0
  
  // Calculate chart dimensions
  const width = 400
  const height = 200
  const padding = 20
  
  const minPrice = Math.min(...data.map(d => d.price))
  const maxPrice = Math.max(...data.map(d => d.price))
  const priceRange = maxPrice - minPrice
  
  // Generate SVG path
  const pathData = data.map((point, index) => {
    const x = padding + (index / (data.length - 1)) * (width - 2 * padding)
    const y = height - padding - ((point.price - minPrice) / priceRange) * (height - 2 * padding)
    return `${index === 0 ? 'M' : 'L'} $