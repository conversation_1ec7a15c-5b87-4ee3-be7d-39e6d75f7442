import express from 'express'
import { createServer } from 'http'
import { Server } from 'socket.io'
import cors from 'cors'
import dotenv from 'dotenv'
import { GameManager } from './game/GameManager'
import { authRoutes } from './routes/auth'
import { gameRoutes } from './routes/game'
import { leaderboardRoutes } from './routes/leaderboard'

dotenv.config()

const app = express()
const server = createServer(app)
const io = new Server(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
})

// Middleware
app.use(cors())
app.use(express.json())

// Routes
app.use('/api/auth', authRoutes)
app.use('/api/game', gameRoutes)
app.use('/api/leaderboard', leaderboardRoutes)

// Game Manager
const gameManager = new GameManager(io)

// Socket.io connection handling
io.on('connection', (socket) => {
  const clientIP = socket.handshake.address
  const userAgent = socket.handshake.headers['user-agent']
  const timestamp = new Date().toLocaleTimeString('de-DE')
  
  console.log(`\n🎮 [${timestamp}] NEW PLAYER CONNECTED`)
  console.log(`   👤 Socket ID: ${socket.id}`)
  console.log(`   🌐 IP Address: ${clientIP}`)
  console.log(`   💻 User Agent: ${userAgent?.substring(0, 50)}...`)
  console.log(`   📊 Total Connections: ${io.engine.clientsCount}`)

  socket.on('join-lobby', () => {
    socket.join('lobby')
    gameManager.addPlayerToLobby(socket)
  })

  socket.on('create-room', (roomData) => {
    gameManager.createRoom(socket, roomData)
  })

  socket.on('join-room', (roomId) => {
    gameManager.joinRoom(socket, roomId)
  })

  socket.on('leave-room', (roomId) => {
    gameManager.leaveRoom(socket, roomId)
  })

  socket.on('player-move', (data) => {
    gameManager.handlePlayerMove(socket, data)
  })

  socket.on('player-shoot', (data) => {
    gameManager.handlePlayerShoot(socket, data)
  })

  socket.on('chat-message', (data) => {
    gameManager.handleChatMessage(socket, data)
  })

  socket.on('disconnect', (reason) => {
    const timestamp = new Date().toLocaleTimeString('de-DE')
    console.log(`\n👋 [${timestamp}] PLAYER DISCONNECTED`)
    console.log(`   👤 Socket ID: ${socket.id}`)
    console.log(`   📝 Reason: ${reason}`)
    console.log(`   📊 Remaining Connections: ${io.engine.clientsCount - 1}`)
    gameManager.handleDisconnect(socket)
  })
})

const PORT = process.env.PORT || 3001
const HOST = process.env.HOST || 'localhost'

server.listen(PORT, () => {
  const isProduction = process.env.NODE_ENV === 'production'
  
  console.log('\n' + '='.repeat(60))
  console.log('🚀 DAVIO GAME SERVER STARTED SUCCESSFULLY')
  console.log('='.repeat(60))
  
  // Local URLs
  console.log('\n📍 LOCAL ACCESS:')
  console.log(`   🌐 Frontend:     http://localhost:3000`)
  console.log(`   ⚡ Backend API:  http://localhost:${PORT}`)
  console.log(`   🎮 WebSocket:    ws://localhost:${PORT}`)
  
  // Network URLs (get local IP)
  const os = require('os')
  const networkInterfaces = os.networkInterfaces()
  let localIP = 'localhost'
  
  for (const interfaceName in networkInterfaces) {
    const networkInterface = networkInterfaces[interfaceName]
    if (networkInterface) {
      for (const alias of networkInterface) {
        if (alias.family === 'IPv4' && !alias.internal) {
          localIP = alias.address
          break
        }
      }
    }
  }
  
  if (localIP !== 'localhost') {
    console.log('\n🌍 NETWORK ACCESS:')
    console.log(`   🌐 Frontend:     http://${localIP}:3000`)
    console.log(`   ⚡ Backend API:  http://${localIP}:${PORT}`)
    console.log(`   🎮 WebSocket:    ws://${localIP}:${PORT}`)
  }
  
  // Production/Cloud URLs
  if (isProduction) {
    const cloudURL = process.env.CLOUD_URL || 'your-app.vercel.app'
    console.log('\n☁️  CLOUD ACCESS:')
    console.log(`   🌐 Production:   https://${cloudURL}`)
    console.log(`   ⚡ API:          https://api.${cloudURL}`)
  }
  
  console.log('\n📊 SERVER STATUS:')
  console.log(`   🔧 Environment:  ${process.env.NODE_ENV || 'development'}`)
  console.log(`   📡 Port:         ${PORT}`)
  console.log(`   🎯 CORS Origin:  http://localhost:3000`)
  console.log(`   💾 Database:     ${process.env.MONGODB_URI ? '✅ Connected' : '❌ Not configured'}`)
  
  console.log('\n🎮 GAME FEATURES:')
  console.log('   ✅ Real-time multiplayer')
  console.log('   ✅ WebSocket connections')
  console.log('   ✅ Room management')
  console.log('   ✅ Player authentication')
  console.log('   ✅ Leaderboards')
  
  console.log('\n📝 API ENDPOINTS:')
  console.log(`   🔐 Auth:         http://localhost:${PORT}/api/auth`)
  console.log(`   🎮 Game:         http://localhost:${PORT}/api/game`)
  console.log(`   🏆 Leaderboard:  http://localhost:${PORT}/api/leaderboard`)
  
  console.log('\n' + '='.repeat(60))
  console.log('🎯 Ready for connections! Start playing at http://localhost:3000')
  console.log('='.repeat(60) + '\n')
})