import { Server, Socket } from 'socket.io'

interface Player {
  id: string
  username: string
  x: number
  y: number
  angle: number
  health: number
  kills: number
  deaths: number
  roomId?: string
  isReady: boolean
}

interface Room {
  id: string
  name: string
  maxPlayers: number
  gameMode: string
  map: string
  players: Map<string, Player>
  status: 'waiting' | 'playing' | 'finished'
  gameState: GameState
}

interface GameState {
  bullets: Bullet[]
  powerUps: PowerUp[]
  gameTime: number
  scoreLimit: number
}

interface Bullet {
  id: string
  x: number
  y: number
  vx: number
  vy: number
  playerId: string
  damage: number
}

interface PowerUp {
  id: string
  type: 'health' | 'ammo' | 'speed'
  x: number
  y: number
  active: boolean
}

export class GameManager {
  private io: Server
  private players: Map<string, Player> = new Map()
  private rooms: Map<string, Room> = new Map()
  private gameLoops: Map<string, NodeJS.Timeout> = new Map()

  constructor(io: Server) {
    this.io = io
    this.initializeDefaultRooms()
  }

  private initializeDefaultRooms() {
    const defaultRooms = [
      {
        id: 'room1',
        name: 'Noob Friendly',
        maxPlayers: 8,
        gameMode: 'Deathmatch',
        map: 'Arena'
      },
      {
        id: 'room2',
        name: 'Pro Players Only',
        maxPlayers: 8,
        gameMode: 'Team Deathmatch',
        map: 'Desert'
      }
    ]

    defaultRooms.forEach(roomData => {
      const room: Room = {
        ...roomData,
        players: new Map(),
        status: 'waiting',
        gameState: {
          bullets: [],
          powerUps: [],
          gameTime: 0,
          scoreLimit: 25
        }
      }
      this.rooms.set(room.id, room)
    })
  }

  addPlayerToLobby(socket: Socket) {
    const player: Player = {
      id: socket.id,
      username: `Player${Math.floor(Math.random() * 1000)}`,
      x: 100,
      y: 100,
      angle: 0,
      health: 100,
      kills: 0,
      deaths: 0,
      isReady: false
    }

    this.players.set(socket.id, player)
    this.broadcastLobbyUpdate()
  }

  createRoom(socket: Socket, roomData: any) {
    const roomId = `room_${Date.now()}`
    const room: Room = {
      id: roomId,
      name: roomData.name || 'Custom Room',
      maxPlayers: roomData.maxPlayers || 8,
      gameMode: roomData.gameMode || 'Deathmatch',
      map: roomData.map || 'Arena',
      players: new Map(),
      status: 'waiting',
      gameState: {
        bullets: [],
        powerUps: [],
        gameTime: 0,
        scoreLimit: 25
      }
    }

    this.rooms.set(roomId, room)
    
    const timestamp = new Date().toLocaleTimeString('de-DE')
    console.log(`\n🏠 [${timestamp}] ROOM CREATED`)
    console.log(`   🆔 Room ID: ${roomId}`)
    console.log(`   📝 Name: ${room.name}`)
    console.log(`   🎮 Mode: ${room.gameMode}`)
    console.log(`   🗺️  Map: ${room.map}`)
    console.log(`   👥 Max Players: ${room.maxPlayers}`)
    
    this.joinRoom(socket, roomId)
    this.broadcastLobbyUpdate()
  }

  joinRoom(socket: Socket, roomId: string) {
    const room = this.rooms.get(roomId)
    const player = this.players.get(socket.id)

    if (!room || !player) return

    if (room.players.size >= room.maxPlayers) {
      socket.emit('room-full')
      return
    }

    // Leave current room if any
    if (player.roomId) {
      this.leaveRoom(socket, player.roomId)
    }

    // Join new room
    player.roomId = roomId
    player.x = Math.random() * 800
    player.y = Math.random() * 600
    player.health = 100
    
    room.players.set(socket.id, player)
    socket.join(roomId)

    // Start game if enough players
    if (room.players.size >= 2 && room.status === 'waiting') {
      this.startGame(roomId)
    }

    this.broadcastRoomUpdate(roomId)
    this.broadcastLobbyUpdate()
  }

  leaveRoom(socket: Socket, roomId: string) {
    const room = this.rooms.get(roomId)
    const player = this.players.get(socket.id)

    if (!room || !player) return

    room.players.delete(socket.id)
    player.roomId = undefined
    socket.leave(roomId)

    // Stop game if not enough players
    if (room.players.size < 2 && room.status === 'playing') {
      this.stopGame(roomId)
    }

    this.broadcastRoomUpdate(roomId)
    this.broadcastLobbyUpdate()
  }

  handlePlayerMove(socket: Socket, data: any) {
    const player = this.players.get(socket.id)
    if (!player || !player.roomId) return

    const room = this.rooms.get(player.roomId)
    if (!room || room.status !== 'playing') return

    // Update player position
    player.x = Math.max(0, Math.min(800, data.x))
    player.y = Math.max(0, Math.min(600, data.y))
    player.angle = data.angle

    // Broadcast to room
    socket.to(player.roomId).emit('player-moved', {
      playerId: socket.id,
      x: player.x,
      y: player.y,
      angle: player.angle
    })
  }

  handlePlayerShoot(socket: Socket, data: any) {
    const player = this.players.get(socket.id)
    if (!player || !player.roomId) return

    const room = this.rooms.get(player.roomId)
    if (!room || room.status !== 'playing') return

    // Create bullet
    const bullet: Bullet = {
      id: `bullet_${Date.now()}_${socket.id}`,
      x: player.x,
      y: player.y,
      vx: Math.cos(player.angle * Math.PI / 180) * 10,
      vy: Math.sin(player.angle * Math.PI / 180) * 10,
      playerId: socket.id,
      damage: 25
    }

    room.gameState.bullets.push(bullet)

    // Broadcast bullet to room
    this.io.to(player.roomId).emit('bullet-fired', bullet)
  }

  handleChatMessage(socket: Socket, data: any) {
    const player = this.players.get(socket.id)
    if (!player) return

    const message = {
      playerId: socket.id,
      username: player.username,
      message: data.message,
      timestamp: Date.now()
    }

    if (player.roomId) {
      this.io.to(player.roomId).emit('chat-message', message)
    } else {
      this.io.to('lobby').emit('chat-message', message)
    }
  }

  handleDisconnect(socket: Socket) {
    const player = this.players.get(socket.id)
    if (player && player.roomId) {
      this.leaveRoom(socket, player.roomId)
    }
    this.players.delete(socket.id)
    this.broadcastLobbyUpdate()
  }

  private startGame(roomId: string) {
    const room = this.rooms.get(roomId)
    if (!room) return

    room.status = 'playing'
    room.gameState.gameTime = 0

    // Initialize power-ups
    this.spawnPowerUps(room)

    // Start game loop
    const gameLoop = setInterval(() => {
      this.updateGame(roomId)
    }, 1000 / 60) // 60 FPS

    this.gameLoops.set(roomId, gameLoop)

    const timestamp = new Date().toLocaleTimeString('de-DE')
    console.log(`\n🎯 [${timestamp}] GAME STARTED`)
    console.log(`   🏠 Room: ${room.name} (${roomId})`)
    console.log(`   🎮 Mode: ${room.gameMode}`)
    console.log(`   👥 Players: ${room.players.size}/${room.maxPlayers}`)
    console.log(`   🗺️  Map: ${room.map}`)
    console.log(`   ⚡ Running at 60 FPS`)

    this.io.to(roomId).emit('game-started', {
      roomId,
      players: Array.from(room.players.values())
    })
  }

  private stopGame(roomId: string) {
    const room = this.rooms.get(roomId)
    if (!room) return

    room.status = 'waiting'
    
    const gameLoop = this.gameLoops.get(roomId)
    if (gameLoop) {
      clearInterval(gameLoop)
      this.gameLoops.delete(roomId)
    }

    this.io.to(roomId).emit('game-stopped')
  }

  private updateGame(roomId: string) {
    const room = this.rooms.get(roomId)
    if (!room || room.status !== 'playing') return

    // Update bullets
    room.gameState.bullets = room.gameState.bullets.filter(bullet => {
      bullet.x += bullet.vx
      bullet.y += bullet.vy

      // Check collision with players
      room.players.forEach((player, playerId) => {
        if (playerId !== bullet.playerId) {
          const distance = Math.sqrt(
            Math.pow(bullet.x - player.x, 2) + Math.pow(bullet.y - player.y, 2)
          )
          
          if (distance < 15) {
            // Hit!
            player.health -= bullet.damage
            
            if (player.health <= 0) {
              this.handlePlayerDeath(roomId, playerId, bullet.playerId)
            }

            this.io.to(roomId).emit('player-hit', {
              playerId,
              damage: bullet.damage,
              health: player.health
            })

            return false // Remove bullet
          }
        }
      })

      // Remove bullets that are out of bounds
      return bullet.x > 0 && bullet.x < 800 && bullet.y > 0 && bullet.y < 600
    })

    // Update game time
    room.gameState.gameTime += 1/60

    // Broadcast game state
    this.io.to(roomId).emit('game-update', {
      bullets: room.gameState.bullets,
      players: Array.from(room.players.values()),
      gameTime: room.gameState.gameTime
    })
  }

  private handlePlayerDeath(roomId: string, deadPlayerId: string, killerPlayerId: string) {
    const room = this.rooms.get(roomId)
    if (!room) return

    const deadPlayer = room.players.get(deadPlayerId)
    const killerPlayer = room.players.get(killerPlayerId)

    if (deadPlayer && killerPlayer) {
      deadPlayer.deaths++
      killerPlayer.kills++
      
      const timestamp = new Date().toLocaleTimeString('de-DE')
      console.log(`\n💀 [${timestamp}] PLAYER ELIMINATED`)
      console.log(`   🎯 ${killerPlayer.username} eliminated ${deadPlayer.username}`)
      console.log(`   📊 ${killerPlayer.username}: ${killerPlayer.kills} kills`)
      console.log(`   🏠 Room: ${room.name}`)
      
      // Respawn player
      deadPlayer.health = 100
      deadPlayer.x = Math.random() * 800
      deadPlayer.y = Math.random() * 600

      this.io.to(roomId).emit('player-killed', {
        deadPlayer: deadPlayerId,
        killer: killerPlayerId,
        killerKills: killerPlayer.kills
      })

      // Check win condition
      if (killerPlayer.kills >= room.gameState.scoreLimit) {
        this.endGame(roomId, killerPlayerId)
      }
    }
  }

  private endGame(roomId: string, winnerId: string) {
    const room = this.rooms.get(roomId)
    if (!room) return

    const winner = room.players.get(winnerId)
    room.status = 'finished'
    
    const gameLoop = this.gameLoops.get(roomId)
    if (gameLoop) {
      clearInterval(gameLoop)
      this.gameLoops.delete(roomId)
    }

    const timestamp = new Date().toLocaleTimeString('de-DE')
    console.log(`\n🏆 [${timestamp}] GAME FINISHED`)
    console.log(`   👑 Winner: ${winner?.username || 'Unknown'}`)
    console.log(`   🎯 Final Score: ${winner?.kills || 0}/${room.gameState.scoreLimit}`)
    console.log(`   🏠 Room: ${room.name}`)
    console.log(`   ⏱️  Game Duration: ${Math.floor(room.gameState.gameTime / 60)}:${Math.floor(room.gameState.gameTime % 60).toString().padStart(2, '0')}`)

    this.io.to(roomId).emit('game-ended', {
      winner: winnerId,
      finalScores: Array.from(room.players.values())
    })

    // Reset room after 10 seconds
    setTimeout(() => {
      room.status = 'waiting'
      room.players.forEach(player => {
        player.kills = 0
        player.deaths = 0
        player.health = 100
      })
      console.log(`\n🔄 [${new Date().toLocaleTimeString('de-DE')}] Room ${room.name} reset and ready for new game`)
    }, 10000)
  }

  private spawnPowerUps(room: Room) {
    const powerUpTypes: ('health' | 'ammo' | 'speed')[] = ['health', 'ammo', 'speed']
    
    for (let i = 0; i < 5; i++) {
      const powerUp: PowerUp = {
        id: `powerup_${i}`,
        type: powerUpTypes[Math.floor(Math.random() * powerUpTypes.length)],
        x: Math.random() * 800,
        y: Math.random() * 600,
        active: true
      }
      room.gameState.powerUps.push(powerUp)
    }
  }

  private broadcastLobbyUpdate() {
    const roomList = Array.from(this.rooms.values()).map(room => ({
      id: room.id,
      name: room.name,
      players: room.players.size,
      maxPlayers: room.maxPlayers,
      gameMode: room.gameMode,
      map: room.map,
      status: room.status
    }))

    this.io.to('lobby').emit('lobby-update', {
      rooms: roomList,
      onlinePlayers: this.players.size
    })
  }

  private broadcastRoomUpdate(roomId: string) {
    const room = this.rooms.get(roomId)
    if (!room) return

    this.io.to(roomId).emit('room-update', {
      players: Array.from(room.players.values()),
      gameState: room.gameState,
      status: room.status
    })
  }
}