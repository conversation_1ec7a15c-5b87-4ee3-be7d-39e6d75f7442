import { Router } from 'express'

const router = Router()

// Mock leaderboard data
const leaderboardData = [
  {
    rank: 1,
    username: 'ProGamer2024',
    kills: 2847,
    deaths: 1156,
    kd: 2.46,
    wins: 234,
    level: 45,
    xp: 15670
  },
  {
    rank: 2,
    username: 'SniperE<PERSON>',
    kills: 2634,
    deaths: 1289,
    kd: 2.04,
    wins: 198,
    level: 42,
    xp: 14230
  },
  {
    rank: 3,
    username: 'FragMaster',
    kills: 2456,
    deaths: 1345,
    kd: 1.83,
    wins: 187,
    level: 39,
    xp: 13450
  },
  {
    rank: 4,
    username: 'HeadshotKing',
    kills: 2234,
    deaths: 1123,
    kd: 1.99,
    wins: 176,
    level: 37,
    xp: 12890
  },
  {
    rank: 5,
    username: '<PERSON><PERSON>',
    kills: 2156,
    deaths: 1234,
    kd: 1.75,
    wins: 165,
    level: 35,
    xp: 12340
  },
  {
    rank: 6,
    username: '<PERSON><PERSON><PERSON>',
    kills: 2089,
    deaths: 1456,
    kd: 1.43,
    wins: 154,
    level: 33,
    xp: 11780
  },
  {
    rank: 7,
    username: '<PERSON>pray<PERSON><PERSON>',
    kills: 1987,
    deaths: 1567,
    kd: 1.27,
    wins: 143,
    level: 31,
    xp: 11230
  },
  {
    rank: 8,
    username: '<PERSON><PERSON><PERSON><PERSON>',
    kills: 1876,
    deaths: 1234,
    kd: 1.52,
    wins: 132,
    level: 29,
    xp: 10670
  },
  {
    rank: 9,
    username: 'QuickScope',
    kills: 1765,
    deaths: 1345,
    kd: 1.31,
    wins: 121,
    level: 27,
    xp: 10120
  },
  {
    rank: 10,
    username: 'NoobSlayer',
    kills: 1654,
    deaths: 1456,
    kd: 1.14,
    wins: 110,
    level: 25,
    xp: 9560
  }
]

// Get global leaderboard
router.get('/global', (req, res) => {
  const { page = 1, limit = 10, sortBy = 'kills' } = req.query

  let sortedData = [...leaderboardData]

  // Sort by different criteria
  switch (sortBy) {
    case 'kd':
      sortedData.sort((a, b) => b.kd - a.kd)
      break
    case 'wins':
      sortedData.sort((a, b) => b.wins - a.wins)
      break
    case 'level':
      sortedData.sort((a, b) => b.level - a.level)
      break
    default:
      sortedData.sort((a, b) => b.kills - a.kills)
  }

  // Update ranks based on sort
  sortedData.forEach((player, index) => {
    player.rank = index + 1
  })

  const startIndex = (Number(page) - 1) * Number(limit)
  const endIndex = startIndex + Number(limit)
  const paginatedData = sortedData.slice(startIndex, endIndex)

  res.json({
    leaderboard: paginatedData,
    totalPlayers: sortedData.length,
    currentPage: Number(page),
    totalPages: Math.ceil(sortedData.length / Number(limit)),
    sortBy
  })
})

// Get weekly leaderboard
router.get('/weekly', (req, res) => {
  // Mock weekly data (would be filtered by date in production)
  const weeklyData = leaderboardData.map(player => ({
    ...player,
    kills: Math.floor(player.kills * 0.1), // Simulate weekly kills
    deaths: Math.floor(player.deaths * 0.1),
    wins: Math.floor(player.wins * 0.15)
  })).sort((a, b) => b.kills - a.kills)

  weeklyData.forEach((player, index) => {
    player.rank = index + 1
  })

  res.json({
    leaderboard: weeklyData.slice(0, 10),
    period: 'weekly',
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    endDate: new Date().toISOString()
  })
})

// Get player stats
router.get('/player/:username', (req, res) => {
  const { username } = req.params
  
  const player = leaderboardData.find(p => 
    p.username.toLowerCase() === username.toLowerCase()
  )

  if (!player) {
    return res.status(404).json({ message: 'Player not found' })
  }

  // Add additional stats
  const detailedStats = {
    ...player,
    gamesPlayed: player.wins + Math.floor(player.wins * 0.7), // Estimate total games
    accuracy: Math.floor(Math.random() * 30) + 60, // Mock accuracy
    headshots: Math.floor(player.kills * 0.25), // Mock headshot percentage
    favoriteWeapon: 'AK-47',
    playtime: `${Math.floor(Math.random() * 200) + 50}h ${Math.floor(Math.random() * 60)}m`,
    lastSeen: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
    achievements: [
      'First Blood',
      'Headshot Master',
      'Killing Spree',
      'Domination',
      'Unstoppable'
    ]
  }

  res.json(detailedStats)
})

// Get top weapons
router.get('/weapons', (req, res) => {
  const weaponStats = [
    {
      weapon: 'AK-47',
      kills: 45670,
      usage: 34.5,
      avgDamage: 36,
      headshots: 11420
    },
    {
      weapon: 'M4A4',
      kills: 37340,
      usage: 28.2,
      avgDamage: 33,
      headshots: 9335
    },
    {
      weapon: 'AWP',
      kills: 20780,
      usage: 15.7,
      avgDamage: 115,
      headshots: 18702
    },
    {
      weapon: 'Glock-18',
      kills: 16020,
      usage: 12.1,
      avgDamage: 28,
      headshots: 3204
    },
    {
      weapon: 'Desert Eagle',
      kills: 12580,
      usage: 9.5,
      avgDamage: 63,
      headshots: 6290
    }
  ]

  res.json(weaponStats)
})

export { router as leaderboardRoutes }