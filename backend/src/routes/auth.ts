import { Router } from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

const router = Router()

// Mock user database (in production, use MongoDB)
const users: any[] = []

// Register
router.post('/register', async (req, res) => {
  try {
    const { username, email, password } = req.body

    // Check if user exists
    const existingUser = users.find(u => u.email === email || u.username === username)
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' })
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10)

    // Create user
    const user = {
      id: Date.now().toString(),
      username,
      email,
      password: hashedPassword,
      stats: {
        kills: 0,
        deaths: 0,
        wins: 0,
        gamesPlayed: 0,
        level: 1,
        xp: 0
      },
      createdAt: new Date()
    }

    users.push(user)

    // Generate JWT
    const token = jwt.sign(
      { userId: user.id, username: user.username },
      process.env.JWT_SECRET || 'fallback_secret',
      { expiresIn: '7d' }
    )

    res.status(201).json({
      message: 'User created successfully',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        stats: user.stats
      }
    })
  } catch (error) {
    res.status(500).json({ message: 'Server error' })
  }
})

// Login
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body

    // Find user
    const user = users.find(u => u.email === email)
    if (!user) {
      return res.status(400).json({ message: 'Invalid credentials' })
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return res.status(400).json({ message: 'Invalid credentials' })
    }

    // Generate JWT
    const token = jwt.sign(
      { userId: user.id, username: user.username },
      process.env.JWT_SECRET || 'fallback_secret',
      { expiresIn: '7d' }
    )

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        stats: user.stats
      }
    })
  } catch (error) {
    res.status(500).json({ message: 'Server error' })
  }
})

// Get profile
router.get('/profile', (req, res) => {
  // In production, verify JWT token here
  const mockUser = {
    id: '1',
    username: 'TestPlayer',
    email: '<EMAIL>',
    stats: {
      kills: 247,
      deaths: 156,
      wins: 89,
      gamesPlayed: 145,
      level: 15,
      xp: 2450
    }
  }

  res.json({ user: mockUser })
})

export { router as authRoutes }