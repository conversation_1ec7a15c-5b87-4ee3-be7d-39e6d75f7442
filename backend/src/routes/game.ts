import { Router } from 'express'

const router = Router()

// Get game stats
router.get('/stats', (req, res) => {
  const stats = {
    totalPlayers: 1247,
    activeGames: 23,
    totalGames: 15847,
    averagePlayTime: '12:34',
    topWeapons: [
      { name: 'AK-47', usage: 34.5 },
      { name: 'M4A4', usage: 28.2 },
      { name: 'AWP', usage: 15.7 },
      { name: 'Glock-18', usage: 12.1 },
      { name: 'Desert Eagle', usage: 9.5 }
    ]
  }

  res.json(stats)
})

// Get weapons data
router.get('/weapons', (req, res) => {
  const weapons = [
    {
      id: 'ak47',
      name: 'AK-47',
      type: 'Assault Rifle',
      damage: 36,
      fireRate: 600,
      accuracy: 73,
      range: 85,
      price: 2700,
      description: 'Powerful and popular AK fires 7.62mm Soviet rounds and is the preferred assault rifle of many terrorist organizations.'
    },
    {
      id: 'm4a4',
      name: 'M4A4',
      type: 'Assault Rifle',
      damage: 33,
      fireRate: 666,
      accuracy: 75,
      range: 88,
      price: 3100,
      description: 'More accurate but less damaging than its AK-47 counterpart, the M4A4 is the full-auto assault rifle of choice for CTs.'
    },
    {
      id: 'awp',
      name: 'AWP',
      type: 'Sniper Rifle',
      damage: 115,
      fireRate: 41,
      accuracy: 95,
      range: 98,
      price: 4750,
      description: 'High risk and high reward, the infamous AWP is recognizable by its signature report and one-shot, one-kill policy.'
    },
    {
      id: 'glock18',
      name: 'Glock-18',
      type: 'Pistol',
      damage: 28,
      fireRate: 400,
      accuracy: 56,
      range: 50,
      price: 200,
      description: 'The Glock 18 Select Fire has a larger magazine than other pistols, giving it a distinct advantage in early-round encounters.'
    },
    {
      id: 'deagle',
      name: 'Desert Eagle',
      type: 'Pistol',
      damage: 63,
      fireRate: 267,
      accuracy: 65,
      range: 75,
      price: 700,
      description: 'As expensive as it is powerful, the Desert Eagle is an iconic pistol that is difficult to master but surprisingly accurate at long range.'
    }
  ]

  res.json(weapons)
})

// Get maps data
router.get('/maps', (req, res) => {
  const maps = [
    {
      id: 'arena',
      name: 'Arena',
      description: 'Close-quarters combat in a futuristic arena',
      playerCount: '2-8',
      gameMode: ['Deathmatch', 'Team Deathmatch'],
      image: '/maps/arena.jpg'
    },
    {
      id: 'desert',
      name: 'Desert Storm',
      description: 'Open desert battlefield with long sightlines',
      playerCount: '4-12',
      gameMode: ['Deathmatch', 'Capture the Flag'],
      image: '/maps/desert.jpg'
    },
    {
      id: 'urban',
      name: 'Urban Warfare',
      description: 'City streets with multiple levels and cover',
      playerCount: '6-16',
      gameMode: ['Team Deathmatch', 'Domination'],
      image: '/maps/urban.jpg'
    },
    {
      id: 'factory',
      name: 'Industrial Complex',
      description: 'Industrial facility with tight corridors',
      playerCount: '4-10',
      gameMode: ['Deathmatch', 'King of the Hill'],
      image: '/maps/factory.jpg'
    }
  ]

  res.json(maps)
})

// Get game modes
router.get('/modes', (req, res) => {
  const gameModes = [
    {
      id: 'deathmatch',
      name: 'Deathmatch',
      description: 'Free-for-all combat. First to reach kill limit wins.',
      maxPlayers: 8,
      duration: '10 minutes',
      objective: 'Reach 25 kills'
    },
    {
      id: 'team-deathmatch',
      name: 'Team Deathmatch',
      description: 'Team vs team combat. First team to reach kill limit wins.',
      maxPlayers: 12,
      duration: '15 minutes',
      objective: 'Team reaches 50 kills'
    },
    {
      id: 'capture-flag',
      name: 'Capture the Flag',
      description: 'Capture the enemy flag and return it to your base.',
      maxPlayers: 16,
      duration: '20 minutes',
      objective: 'Capture 3 flags'
    },
    {
      id: 'king-hill',
      name: 'King of the Hill',
      description: 'Control the central area to earn points.',
      maxPlayers: 10,
      duration: '12 minutes',
      objective: 'Control for 5 minutes'
    }
  ]

  res.json(gameModes)
})

export { router as gameRoutes }